import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngxs/store';
import { OneFunnelInsightModel } from '../../model/one-funnel.model';
import { CustomerModuleService } from 'src/app/modules/customer/service/customer-module.service';
import { SetOneFunnelDetailAction } from '../../state/one-funnel.actions';

@Component({
  selector: 'app-one-funnel-card',
  templateUrl: './one-funnel-card.component.html',
  styleUrls: ['./one-funnel-card.component.scss'],
})
export class OneFunnelCardComponent implements OnInit {
  @Input() insight: OneFunnelInsightModel;
  @Input() isDetailView: boolean = false;

  constructor(
    private readonly customerModuleService: CustomerModuleService,
    private readonly store: Store,
    private readonly router: Router
  ) {}

  ngOnInit() {}

  getIconClass(iconName?: string): string {
    switch (iconName) {
      case 'edit':
        return 'icon-edit';
      case 'delete':
        return 'icon-delete';
      case 'add':
        return 'icon-add';
      case 'save':
        return 'icon-save';
      default:
        return 'icon-smu-reading';
    }
  }


  shouldShowSmuButton(): boolean {
    return this.insight?.flowKey === 'ONE_ST_004';
  }

  shouldShowGoToEquipmentButton(): boolean {
    return !!this.insight?.serialNumber;
  }

  onCardClick(): void {
    if (!this.isDetailView && this.insight) {
      this.store.dispatch(new SetOneFunnelDetailAction(this.insight));
      this.router.navigateByUrl('/one-funnel/detail');
    }
  }

  onGoToEquipment(): void {
    if (this.insight?.equipmentNumber) {
      this.customerModuleService.openEquipmentModule(
        this.insight.customerNumber,
        this.insight.equipmentNumber
      );
    }
  }
}
